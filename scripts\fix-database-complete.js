#!/usr/bin/env node

require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

async function fixDatabaseComplete() {
  console.log('🔧 QuestLink Database Complete Fix...\n');

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    console.log('❌ Missing environment variables');
    console.log('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
    process.exit(1);
  }

  const supabase = createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });

  try {
    console.log('Step 1: Applying database fixes...');
    
    // Read and execute the fix script
    const fixScript = fs.readFileSync(
      path.join(__dirname, '../supabase/fix-database-issues.sql'), 
      'utf8'
    );

    const { error: fixError } = await supabase.rpc('exec_sql', { sql: fixScript });
    
    if (fixError) {
      console.log('❌ Error applying database fixes:', fixError.message);
      return;
    }
    console.log('✅ Database fixes applied successfully');

    console.log('\nStep 2: Creating sample auth users...');
    
    // Sample users to create
    const sampleUsers = [
      {
        id: '550e8400-e29b-41d4-a716-446655440000',
        email: '<EMAIL>',
        password: 'admin123',
        first_name: 'Admin',
        last_name: 'User',
        mobile_number: '+639171234567',
        complete_address: '123 Admin St, Quezon City, Metro Manila'
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440001',
        email: '<EMAIL>',
        password: 'password123',
        first_name: 'John',
        last_name: 'Doe',
        mobile_number: '+639171234568',
        complete_address: '456 Main St, Makati City, Metro Manila'
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440002',
        email: '<EMAIL>',
        password: 'password123',
        first_name: 'Jane',
        last_name: 'Smith',
        mobile_number: '+639171234569',
        complete_address: '789 Oak Ave, Taguig City, Metro Manila'
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440003',
        email: '<EMAIL>',
        password: 'password123',
        first_name: 'Mike',
        last_name: 'Wilson',
        mobile_number: '+639171234570',
        complete_address: '321 Pine St, Pasig City, Metro Manila'
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440004',
        email: '<EMAIL>',
        password: 'password123',
        first_name: 'Sarah',
        last_name: 'Johnson',
        mobile_number: '+639171234571',
        complete_address: '654 Elm St, Mandaluyong City, Metro Manila'
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440005',
        email: '<EMAIL>',
        password: 'password123',
        first_name: 'Alex',
        last_name: 'Brown',
        mobile_number: '+639171234572',
        complete_address: '987 Maple Ave, San Juan City, Metro Manila'
      }
    ];

    let authSuccessCount = 0;
    let authErrorCount = 0;

    for (const user of sampleUsers) {
      try {
        // Delete existing auth user if it exists
        await supabase.auth.admin.deleteUser(user.id);
        
        // Create new auth user with specific ID
        const { data, error } = await supabase.auth.admin.createUser({
          email: user.email,
          password: user.password,
          user_id: user.id,
          email_confirm: true,
          user_metadata: {
            first_name: user.first_name,
            last_name: user.last_name,
            mobile_number: user.mobile_number,
            complete_address: user.complete_address
          }
        });

        if (error) {
          if (error.message.includes('already registered')) {
            console.log(`  ⚠️  Auth account already exists for ${user.email}`);
          } else {
            console.log(`  ❌ Error creating auth account for ${user.email}: ${error.message}`);
            authErrorCount++;
          }
        } else {
          console.log(`  ✅ Auth account created for ${user.email}`);
          authSuccessCount++;
        }

        // Small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 500));

      } catch (err) {
        console.log(`  ❌ Unexpected error for ${user.email}: ${err.message}`);
        authErrorCount++;
      }
    }

    console.log(`\n📊 Auth Creation Results:`);
    console.log(`✅ Successful: ${authSuccessCount}`);
    console.log(`❌ Errors: ${authErrorCount}`);

    console.log('\nStep 3: Applying sample data fixes...');
    
    // Read and execute the sample data fix script
    const sampleScript = fs.readFileSync(
      path.join(__dirname, '../supabase/create-sample-users-fixed.sql'), 
      'utf8'
    );

    const { error: sampleError } = await supabase.rpc('exec_sql', { sql: sampleScript });
    
    if (sampleError) {
      console.log('❌ Error applying sample data fixes:', sampleError.message);
    } else {
      console.log('✅ Sample data fixes applied successfully');
    }

    console.log('\nStep 4: Verifying setup...');
    
    // Verify the setup
    const { data: verification, error: verifyError } = await supabase
      .rpc('verify_auth_setup');

    if (verifyError) {
      console.log('❌ Error verifying setup:', verifyError.message);
    } else {
      console.log('✅ Setup verification:');
      console.log(`   - Auth users: ${verification.auth_users}`);
      console.log(`   - Public users: ${verification.public_users}`);
      console.log(`   - Profiles: ${verification.profiles}`);
      console.log(`   - Trigger active: ${verification.trigger_active}`);
      console.log(`   - Setup complete: ${verification.setup_complete}`);
    }

    console.log('\nStep 5: Testing authentication...');
    
    // Test login with sample user
    const testClient = createClient(supabaseUrl, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY);
    
    const { data: loginData, error: loginError } = await testClient.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'admin123'
    });

    if (loginError) {
      console.log(`❌ Test login failed: ${loginError.message}`);
    } else {
      console.log(`✅ Test login <NAME_EMAIL>`);
      
      // Test profile fetching
      const { data: userData, error: userError } = await testClient
        .from('users')
        .select(`
          id,
          email,
          first_name,
          last_name,
          user_role,
          profiles (
            id,
            description,
            location
          )
        `)
        .eq('id', loginData.user.id)
        .single();

      if (userError) {
        console.log(`⚠️  Profile fetch failed: ${userError.message}`);
      } else {
        console.log(`✅ Profile fetched: ${userData.first_name} ${userData.last_name} (${userData.user_role})`);
      }

      // Sign out
      await testClient.auth.signOut();
    }

    console.log('\n🎉 Database fix complete!');
    console.log('\n📋 Summary:');
    console.log('✅ Database triggers installed');
    console.log('✅ RLS policies updated');
    console.log('✅ Sample auth users created');
    console.log('✅ Sample data synchronized');
    console.log('✅ Authentication tested');

    console.log('\n🔑 Sample Login Credentials:');
    console.log('================================');
    sampleUsers.forEach(user => {
      console.log(`${user.email} / ${user.password}`);
    });

    console.log('\n🚀 Next Steps:');
    console.log('1. Start development server: npm run dev');
    console.log('2. Test login at: http://localhost:3000/auth/login');
    console.log('3. Test registration at: http://localhost:3000/auth/register');

  } catch (error) {
    console.log('❌ Unexpected error during database fix:', error.message);
    console.log('\nPlease check your environment variables and database connection.');
  }
}

// Helper function to execute SQL (if not available as RPC)
async function executeSql(supabase, sql) {
  // Split SQL into individual statements
  const statements = sql
    .split(';')
    .map(s => s.trim())
    .filter(s => s.length > 0 && !s.startsWith('--'));

  for (const statement of statements) {
    try {
      await supabase.rpc('exec_sql', { sql: statement });
    } catch (error) {
      console.log(`Warning: SQL statement failed: ${error.message}`);
    }
  }
}

fixDatabaseComplete().catch(console.error);
