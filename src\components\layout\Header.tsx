'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { 
  Sword, 
  Menu, 
  X, 
  User, 
  Settings, 
  LogOut, 
  Bell,
  Search,
  Plus
} from 'lucide-react'
import { useAuth } from '@/hooks/useAuth'
import { cn } from '@/utils/cn'

interface HeaderProps {
  className?: string
}

export function Header({ className }: HeaderProps) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const { user, userProfile, signOut, loading } = useAuth()
  const router = useRouter()

  const handleSignOut = async () => {
    await signOut()
    router.push('/')
  }

  const navigation = [
    { name: 'QuestBoard', href: '/quests', icon: Sword },
    { name: '<PERSON>ll<PERSON>oard', href: '/skills', icon: User },
    { name: 'Services', href: '/services', icon: Settings },
  ]

  const userInitials = userProfile 
    ? `${userProfile.first_name[0]}${userProfile.last_name[0]}` 
    : user?.email?.[0]?.toUpperCase() || 'U'

  return (
    <header className={cn(
      "sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",
      className
    )}>
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2 group">
            <div className="relative">
              <Sword className="h-6 w-6 text-primary animate-float group-hover:scale-110 transition-transform duration-300" />
              <div className="absolute inset-0 h-6 w-6 bg-primary/20 rounded-full animate-glow opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            <span className="text-xl font-bold bg-gradient-to-r from-primary via-purple-600 to-pink-600 bg-clip-text text-transparent group-hover:from-purple-600 group-hover:via-pink-600 group-hover:to-primary transition-all duration-300">
              QuestLink
            </span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="group flex items-center space-x-2 text-sm font-medium text-muted-foreground hover:text-foreground transition-all duration-300 px-3 py-2 rounded-lg hover:bg-accent/50"
              >
                <item.icon className="h-4 w-4 group-hover:scale-110 transition-transform duration-300" />
                <span className="group-hover:font-semibold transition-all duration-300">{item.name}</span>
              </Link>
            ))}
          </nav>

          {/* Right Side */}
          <div className="flex items-center space-x-4">
            {/* Search Button (Desktop) */}
            <Button variant="ghost" size="sm" className="hidden md:flex hover:bg-accent/70 transition-all duration-300 hover:scale-105">
              <Search className="h-4 w-4" />
            </Button>

            {user ? (
              <>
                {/* Create Button */}
                <div className="relative group">
                  <Button variant="default" size="sm" className="flex items-center space-x-2">
                    <Plus className="h-4 w-4" />
                    <span>Create</span>
                  </Button>

                  {/* Create Dropdown Menu */}
                  <div className="absolute right-0 top-full mt-2 w-48 bg-background border rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                    <div className="py-1">
                      <Link
                        href="/create/quest"
                        className="flex items-center space-x-2 px-4 py-2 text-sm hover:bg-muted"
                      >
                        <Sword className="h-4 w-4" />
                        <span>Post a Quest</span>
                      </Link>
                      <Link
                        href="/create/skill"
                        className="flex items-center space-x-2 px-4 py-2 text-sm hover:bg-muted"
                      >
                        <User className="h-4 w-4" />
                        <span>Add a Skill</span>
                      </Link>
                      <Link
                        href="/create/service"
                        className="flex items-center space-x-2 px-4 py-2 text-sm hover:bg-muted"
                      >
                        <Settings className="h-4 w-4" />
                        <span>Create Service</span>
                      </Link>
                    </div>
                  </div>
                </div>

                {/* Notifications */}
                <Button variant="ghost" size="sm" className="relative">
                  <Bell className="h-4 w-4" />
                  <Badge 
                    variant="destructive" 
                    className="absolute -top-1 -right-1 h-4 w-4 p-0 text-xs"
                  >
                    3
                  </Badge>
                </Button>

                {/* User Menu */}
                <div className="relative group">
                  <Button variant="ghost" className="flex items-center space-x-2 p-2">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={userProfile?.profiles?.[0]?.profile_picture} />
                      <AvatarFallback className="text-xs">{userInitials}</AvatarFallback>
                    </Avatar>
                    <div className="hidden md:block text-left">
                      <p className="text-sm font-medium">
                        {userProfile?.first_name || user.email}
                      </p>
                      <p className="text-xs text-muted-foreground capitalize">
                        {userProfile?.user_role || 'User'}
                      </p>
                    </div>
                  </Button>

                  {/* Dropdown Menu */}
                  <div className="absolute right-0 top-full mt-2 w-56 bg-background border rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                    <div className="py-1">
                      <Link
                        href="/dashboard"
                        className="flex items-center space-x-2 px-4 py-2 text-sm hover:bg-muted"
                      >
                        <User className="h-4 w-4" />
                        <span>Dashboard</span>
                      </Link>
                      <div className="px-4 py-1">
                        <p className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Manage</p>
                      </div>
                      <Link
                        href="/dashboard/quests"
                        className="flex items-center space-x-2 px-4 py-2 text-sm hover:bg-muted"
                      >
                        <Sword className="h-4 w-4" />
                        <span>My Quests</span>
                      </Link>
                      <Link
                        href="/dashboard/skills"
                        className="flex items-center space-x-2 px-4 py-2 text-sm hover:bg-muted"
                      >
                        <User className="h-4 w-4" />
                        <span>My Skills</span>
                      </Link>
                      <Link
                        href="/dashboard/services"
                        className="flex items-center space-x-2 px-4 py-2 text-sm hover:bg-muted"
                      >
                        <Settings className="h-4 w-4" />
                        <span>My Services</span>
                      </Link>
                      <div className="border-t my-1"></div>
                      <Link
                        href="/profile"
                        className="flex items-center space-x-2 px-4 py-2 text-sm hover:bg-muted"
                      >
                        <Settings className="h-4 w-4" />
                        <span>Profile Settings</span>
                      </Link>
                      <div className="border-t my-1"></div>
                      <button
                        onClick={handleSignOut}
                        className="flex items-center space-x-2 px-4 py-2 text-sm hover:bg-muted w-full text-left"
                      >
                        <LogOut className="h-4 w-4" />
                        <span>Sign Out</span>
                      </button>
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <div className="flex items-center space-x-2">
                <Button variant="ghost" size="sm" asChild>
                  <Link href="/auth/login">Sign In</Link>
                </Button>
                <Button variant="default" size="sm" asChild>
                  <Link href="/auth/register">Sign Up</Link>
                </Button>
              </div>
            )}

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="sm"
              className="md:hidden"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {mobileMenuOpen && (
          <div className="md:hidden border-t py-4">
            <nav className="space-y-2">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="flex items-center space-x-2 px-2 py-2 text-sm font-medium text-muted-foreground hover:text-foreground hover:bg-muted rounded-md"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <item.icon className="h-4 w-4" />
                  <span>{item.name}</span>
                </Link>
              ))}
              
              {!user && (
                <div className="pt-2 space-y-2">
                  <Link
                    href="/auth/login"
                    className="block px-2 py-2 text-sm font-medium text-muted-foreground hover:text-foreground hover:bg-muted rounded-md"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    Sign In
                  </Link>
                  <Link
                    href="/auth/register"
                    className="block px-2 py-2 text-sm font-medium text-muted-foreground hover:text-foreground hover:bg-muted rounded-md"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    Sign Up
                  </Link>
                </div>
              )}
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}
