{"name": "questlink", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "setup-db": "node scripts/setup-questlink-db.js", "setup-env": "node scripts/setup-env.js", "test-connection": "node scripts/test-connection.js", "verify-database": "node scripts/verify-database.js", "create-test-user": "node scripts/create-test-user.js", "cleanup-test-users": "node scripts/cleanup-test-users.js", "list-users": "node scripts/list-users.js", "fix-auth-accounts": "node scripts/fix-auth-accounts.js", "test-auth-flow": "node scripts/test-auth-flow.js", "create-sample-auth-users": "node scripts/create-sample-auth-users.js", "test-complete-auth": "node scripts/test-complete-auth.js", "debug-email-validation": "node scripts/debug-email-validation.js", "test-direct-supabase": "node scripts/test-direct-supabase.js", "fix-database-complete": "node scripts/fix-database-complete.js", "fix-auth-simple": "node scripts/fix-auth-simple.js", "fix-sample-passwords": "node scripts/fix-sample-passwords.js", "cleanup-database": "node scripts/cleanup-database.js", "deploy-auth-trigger": "node scripts/deploy-auth-trigger.js"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-separator": "^1.1.7", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "framer-motion": "^12.18.1", "lucide-react": "^0.518.0", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.3.4", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.5.3", "tailwindcss": "^4", "typescript": "^5", "uuid": "^11.1.0"}}